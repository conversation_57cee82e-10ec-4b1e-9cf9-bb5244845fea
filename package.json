{"name": "tts-yoda-backend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@next-auth/mongodb-adapter": "^1.1.3", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-form": "^0.1.8", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@remixicon/react": "^4.6.0", "axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto": "^1.0.1", "dotenv": "^17.2.1", "fs": "^0.0.1-security", "google-auth-library": "^10.3.0", "i18next": "^25.4.2", "jose": "^6.1.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.542.0", "mongodb": "^6.19.0", "next": "15.5.2", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "nodemailer": "^7.0.5", "path": "^0.12.7", "pg": "^8.16.3", "react": "19.1.1", "react-dom": "19.1.1", "react-i18next": "^15.7.3", "react-leaflet": "^5.0.0", "react-toastify": "^11.0.5", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.7"}}