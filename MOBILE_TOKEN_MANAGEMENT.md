# Mobile Token Management Guide

Bu dokümantasyon mobil uygulamalar için token yönetimi ve refresh token akışını açıklar.

## 🔑 Token Yapısı

### Access Token
- **Süre**: 30 dakika (`vars.token.tokenlifeTime`)
- **Kullanım**: API çağrıları için Authorization header'ında
- **Format**: `Bearer <token>`

### Refresh Token
- **Süre**: 30 gün (`vars.token.refreshtokenLifeTime`)
- **Kullanım**: Access token yenilemek için
- **Güvenlik**: Güvenli storage'da saklanmalı

## 📱 Mobil Uygulamalar İçin Özel Endpoint'ler

### 1. Login Endpoint
```
POST /api/auth/mobile
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "clientId": "optional-client-id"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "User Name",
    "role": "member",
    // ... diğer user bilgileri
  }
}
```

### 2. Mobile Refresh Token Endpoint
```
POST /api/auth/mobile-refresh-token
```

**Headers:**
```
Content-Type: application/json
X-Client-Type: mobile  // Önemli: Mobil client olduğunu belirtir
```

**Request Body:**
```json
{
  "refreshToken": "current-refresh-token",
  "deviceId": "unique-device-id",  // Opsiyonel
  "appVersion": "1.0.0"           // Opsiyonel
}
```

**Success Response (200):**
```json
{
  "token": "new-access-token",
  "refreshToken": "new-refresh-token",
  "expiresIn": "30m",
  "tokenType": "Bearer",
  "message": "Mobile token refresh successful"
}
```

**Error Response (401):**
```json
{
  "message": "Invalid or expired refresh token",
  "error": "INVALID_REFRESH_TOKEN",
  "requiresLogin": true
}
```

## 🔄 Token Refresh Akışı

### Mobil Uygulamada Token Yönetimi

```javascript
class TokenManager {
  constructor() {
    this.accessToken = null;
    this.refreshToken = null;
    this.isRefreshing = false;
  }

  async getValidToken() {
    if (this.isTokenExpired(this.accessToken)) {
      return await this.refreshAccessToken();
    }
    return this.accessToken;
  }

  async refreshAccessToken() {
    if (this.isRefreshing) {
      // Eğer zaten refresh işlemi devam ediyorsa bekle
      return this.waitForRefresh();
    }

    this.isRefreshing = true;

    try {
      const response = await fetch('/api/auth/mobile-refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Client-Type': 'mobile'
        },
        body: JSON.stringify({
          refreshToken: this.refreshToken,
          deviceId: this.getDeviceId(),
          appVersion: this.getAppVersion()
        })
      });

      if (response.ok) {
        const data = await response.json();
        this.accessToken = data.token;
        this.refreshToken = data.refreshToken; // Yeni refresh token
        this.saveTokensSecurely(data.token, data.refreshToken);
        return data.token;
      } else {
        const error = await response.json();
        if (error.requiresLogin) {
          this.clearTokens();
          this.redirectToLogin();
        }
        throw new Error(error.message);
      }
    } finally {
      this.isRefreshing = false;
    }
  }

  async makeAuthenticatedRequest(url, options = {}) {
    const token = await this.getValidToken();
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'X-Client-Type': 'mobile'
      }
    });

    // 401 durumunda bir kez daha refresh dene
    if (response.status === 401) {
      const newToken = await this.refreshAccessToken();
      return fetch(url, {
        ...options,
        headers: {
          ...options.headers,
          'Authorization': `Bearer ${newToken}`,
          'X-Client-Type': 'mobile'
        }
      });
    }

    return response;
  }
}
```

## 🛡️ Güvenlik Önerileri

### 1. Token Storage
- **Access Token**: Memory'de sakla (app restart'ta kaybolur)
- **Refresh Token**: Secure storage'da sakla (Keychain/Keystore)
- **Asla plain text olarak saklamayın**

### 2. Network Security
- **HTTPS kullanın**
- **Certificate pinning** uygulayın
- **X-Client-Type: mobile** header'ını ekleyin

### 3. Error Handling
- **requiresLogin: true** geldiğinde kullanıcıyı login'e yönlendirin
- **Network errors** için retry mekanizması ekleyin
- **Token refresh failures** için fallback stratejisi belirleyin

## 🔧 Middleware Davranışı

### Web vs Mobile
- **Web Uygulamaları**: Middleware otomatik token refresh yapar
- **Mobil Uygulamalar**: Middleware refresh yapmaz, 401 döner
- **Detection**: `X-Client-Type: mobile` header'ı veya User-Agent kontrolü

### Mobile App Detection
```typescript
const isMobileApp = request.headers.get('x-client-type') === 'mobile' || 
                    userAgent.includes('Mobile-App') ||
                    !userAgent.includes('Mozilla');
```

## 📊 Test Etme

Test sayfasında (`/test-token-refresh`) "Test Mobile Refresh" butonunu kullanarak mobil refresh endpoint'ini test edebilirsiniz.

## ⚠️ Önemli Notlar

1. **Mobil uygulamalar kendi token yönetimini yapmalı**
2. **Middleware sadece web uygulamaları için otomatik refresh yapar**
3. **Mobile refresh endpoint hem access hem refresh token'ı yeniler**
4. **requiresLogin: true** geldiğinde kullanıcı tekrar login olmalı
5. **X-Client-Type: mobile** header'ını mutlaka gönderin**
