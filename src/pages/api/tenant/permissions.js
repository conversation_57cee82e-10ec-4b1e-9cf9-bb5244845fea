// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../auth/[...nextauth]';
export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    const user = session?.user;
    // console.log('Tenant Info API called', user);
    const { method } = req;
    switch (method) {
        case 'POST':
            try {
                let customerData = {};
                let permissions = [];
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                    permissions = await getPermissionData(user.tenantData.clientId, customerData);
                }
                res.status(200).json({ data: permissions });
            } catch (error) {
                console.error('Tenant Info API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
        case 'PUT':
            try {
                // Check if user is admin
                if (!user?.tenantData?.isTenantAdmin) {
                    return res.status(403).json({ message: 'Forbidden: Only admins can update permissions' });
                }
                
                if (user?.tenantData?.clientId) {
                    const result = await updatePermissionsData(user.tenantData.clientId, user.tenantData);
                    res.status(200).json({ message: 'Permissions updated successfully', data: result });
                } else {
                    res.status(400).json({ message: 'Client ID not found' });
                }
            } catch (error) {
                console.error('Update Permissions API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
             
            
        default:
            res.setHeader('Allow', ['POST', 'PUT']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}


const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}


const getPermissionData = async (clientId, customerData, fields = {
    createdAt: 0,
}) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.permissions}`;
    const dbClient = client.db(clientDB);
    const q = [
        {
            $project: {
                ...fields,
            }
        }
    ];
    const positions = await dbClient.collection(dbTitleCollName).aggregate(q).toArray();
    // console.log('getPositionsData - aggregation result:', JSON.stringify(tenant, null, 2));
    if (!positions) {
        return null;
    }
    return positions;
}

const updatePermissionsData = async (clientId, customerData) => {
    const client = await clientPromise;
    
    // Get the subanet database for modules
    const subanetDB = client.db(vars.db.dbName);
    const modulesCollection = vars.db.collection.modules;
    
    // Get client database and schema for permissions
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const permissionsCollection = `${clientSchema}${vars.db.actCollections.permissions}`;
    
    // Get all modules from subanet database
    const modules = await subanetDB.collection(modulesCollection).find({ isActive: true }).toArray();
    
    // Get the client database
    const dbClient = client.db(clientDB);
    
    // First, remove all existing permissions
    await dbClient.collection(permissionsCollection).deleteMany({});
    
    // Process each module and add its permissions
    let insertedCount = 0;
    for (const module of modules) {
        if (module.permissions && Array.isArray(module.permissions)) {
            // Create permission documents for this module
            const permissionDocs = module.permissions.map(perm => ({
                module: module.moduleName,
                moduleTitle: module.moduleTitle,
                code: perm.code,
                desc: perm.desc,
                createdAt: new Date(),
                updatedAt: new Date()
            }));
            
            // Insert permissions for this module
            if (permissionDocs.length > 0) {
                await dbClient.collection(permissionsCollection).insertMany(permissionDocs);
                insertedCount += permissionDocs.length;
            }
        }
    }
    
    return {
        message: 'Permissions updated successfully',
        modulesProcessed: modules.length,
        permissionsInserted: insertedCount
    };
};
