import clientPromise, { getCollection } from "@/lib/db/mongodb";
import { vars } from '@/lib/constants';
import * as fnxAuth from "@/lib/fnx/fnx.auth";

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { expiredToken, userEmail } = req.body;
    console.log('Middleware refresh token request for user:', userEmail);
    
    if (!expiredToken || !userEmail) {
      return res.status(400).json({ 
        message: 'Expired token and user email are required',
        error: 'MISSING_PARAMETERS'
      });
    }

    // Find user and get their refresh token
    const dbConn = await clientPromise;
    const users = await getCollection(vars.db.collection.users);
    const user = await users.findOne({
      email: userEmail
    });

    if (!user) {
      console.log('User not found for email:', userEmail);
      return res.status(401).json({ 
        message: 'User not found',
        error: 'USER_NOT_FOUND'
      });
    }

    if (!user.refreshToken) {
      console.log('No refresh token found for user:', userEmail);
      return res.status(401).json({ 
        message: 'No refresh token available',
        error: 'NO_REFRESH_TOKEN'
      });
    }

    // Verify refresh token
    const key = process.env.JWT_KEY;
    if (!key) {
      console.error('JWT_KEY environment variable is not set');
      return res.status(500).json({ 
        message: 'Server configuration error',
        error: 'MISSING_JWT_KEY'
      });
    }

    const tokenValidation = await fnxAuth.verifytoken(user.refreshToken, key);
    if (!tokenValidation.isValid) {
      console.log('Invalid or expired refresh token for user:', userEmail);
      return res.status(401).json({ 
        message: 'Invalid or expired refresh token',
        error: 'INVALID_REFRESH_TOKEN'
      });
    }

    // Create standardized JWT payload
    const jwtPayload = fnxAuth.createJWTPayload(user);

    // Generate new access token
    const newToken = await fnxAuth.jwtx.sign({
      payload: jwtPayload, 
      lifeTime: vars.token.tokenlifeTime
    });
    
    // Save the new access token to the database
    await fnxAuth.saveToken({
      dbConn: dbConn,
      payload: { email: user.email },
      token: newToken,
      refreshToken: user.refreshToken, // Keep the same refresh token
      saveLogin: false
    });
    
    console.log('Token refreshed successfully via middleware for user:', user.email);
    
    return res.status(200).json({
      token: newToken,
      message: 'Token refreshed successfully'
    });

  } catch (error) {
    console.error('Middleware refresh token error:', error);
    
    // Provide more specific error messages
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        message: 'Invalid refresh token format',
        error: 'MALFORMED_TOKEN'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        message: 'Refresh token has expired',
        error: 'EXPIRED_REFRESH_TOKEN'
      });
    }
    
    return res.status(500).json({ 
      message: 'Internal server error during token refresh',
      error: 'INTERNAL_ERROR'
    });
  }
}
