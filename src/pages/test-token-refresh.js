import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { jwtDecode } from 'jwt-decode';

export default function TestTokenRefresh() {
  const { getToken, authenticatedFetch, isAuthenticated, status, isRefreshing, needsRefresh } = useAuth();
  const [tokenInfo, setTokenInfo] = useState(null);
  const [testResults, setTestResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addTestResult = (test, success, message) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const decodeTokenInfo = (token) => {
    try {
      const decoded = jwtDecode(token);
      return {
        email: decoded.email,
        role: decoded.role,
        exp: new Date(decoded.exp * 1000).toLocaleString(),
        timeUntilExpiry: Math.floor((decoded.exp * 1000 - Date.now()) / 1000)
      };
    } catch (error) {
      return { error: error.message };
    }
  };

  const testGetToken = async () => {
    setLoading(true);
    try {
      const token = await getToken();
      if (token) {
        const info = decodeTokenInfo(token);
        setTokenInfo(info);
        addTestResult('Get Token', true, `Token retrieved successfully. Expires in ${info.timeUntilExpiry} seconds`);
      } else {
        addTestResult('Get Token', false, 'No token returned');
      }
    } catch (error) {
      addTestResult('Get Token', false, error.message);
    }
    setLoading(false);
  };

  const testAuthenticatedFetch = async () => {
    setLoading(true);
    try {
      // Test with a protected endpoint that goes through middleware
      const response = await authenticatedFetch('/api/tenant/roles', {
        method: 'GET'
      });

      if (response.ok) {
        const data = await response.json();
        addTestResult('Authenticated Fetch', true, `Protected endpoint accessed successfully`);

        // Check if we got a new token from middleware
        const newToken = response.headers.get('X-New-Token');
        if (newToken) {
          addTestResult('Middleware Token Refresh', true, 'Middleware automatically refreshed token');
          const info = decodeTokenInfo(newToken);
          setTokenInfo(info);
        }
      } else {
        addTestResult('Authenticated Fetch', false, `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      addTestResult('Authenticated Fetch', false, error.message);
    }
    setLoading(false);
  };

  const testRefreshTokenDirectly = async () => {
    setLoading(true);
    try {
      // Get current session to extract refresh token
      const response = await fetch('/api/auth/session');
      const session = await response.json();

      if (!session?.refreshToken && !session?.user?.refreshToken) {
        addTestResult('Direct Refresh', false, 'No refresh token found in session');
        setLoading(false);
        return;
      }

      const refreshToken = session.refreshToken || session.user.refreshToken;

      const refreshResponse = await fetch('/api/auth/refresh-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken }),
      });

      if (refreshResponse.ok) {
        const data = await refreshResponse.json();
        const info = decodeTokenInfo(data.token);
        addTestResult('Direct Refresh', true, `New token generated. Expires in ${info.timeUntilExpiry} seconds`);
      } else {
        const error = await refreshResponse.json();
        addTestResult('Direct Refresh', false, `${refreshResponse.status}: ${error.message}`);
      }
    } catch (error) {
      addTestResult('Direct Refresh', false, error.message);
    }
    setLoading(false);
  };

  const testMiddlewareRefresh = async () => {
    setLoading(true);
    try {
      // Make a direct API call with current token to test middleware refresh
      const token = await getToken();
      if (!token) {
        addTestResult('Middleware Refresh', false, 'No token available');
        setLoading(false);
        return;
      }

      const response = await fetch('/api/tenant/roles', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Check if middleware provided a new token
      const newToken = response.headers.get('X-New-Token');

      if (response.ok) {
        if (newToken) {
          const info = decodeTokenInfo(newToken);
          setTokenInfo(info);
          addTestResult('Middleware Refresh', true, `Middleware refreshed token automatically. New token expires in ${info.timeUntilExpiry} seconds`);
        } else {
          addTestResult('Middleware Refresh', true, 'API call successful, no token refresh needed');
        }
      } else {
        addTestResult('Middleware Refresh', false, `API call failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      addTestResult('Middleware Refresh', false, error.message);
    }
    setLoading(false);
  };

  const testMobileRefresh = async () => {
    setLoading(true);
    try {
      // Get current session to extract refresh token
      const response = await fetch('/api/auth/session');
      const session = await response.json();

      if (!session?.refreshToken && !session?.user?.refreshToken) {
        addTestResult('Mobile Refresh', false, 'No refresh token found in session');
        setLoading(false);
        return;
      }

      const refreshToken = session.refreshToken || session.user.refreshToken;

      // Test mobile refresh endpoint
      const refreshResponse = await fetch('/api/auth/mobile-refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Client-Type': 'mobile' // Mobil client olduğunu belirt
        },
        body: JSON.stringify({
          refreshToken,
          deviceId: 'test-device-123',
          appVersion: '1.0.0'
        }),
      });

      if (refreshResponse.ok) {
        const data = await refreshResponse.json();
        const info = decodeTokenInfo(data.token);
        addTestResult('Mobile Refresh', true, `Mobile refresh successful. New token expires in ${info.timeUntilExpiry} seconds. New refresh token also provided.`);
      } else {
        const error = await refreshResponse.json();
        addTestResult('Mobile Refresh', false, `${refreshResponse.status}: ${error.message} (${error.error})`);
      }
    } catch (error) {
      addTestResult('Mobile Refresh', false, error.message);
    }
    setLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
    setTokenInfo(null);
  };

  if (status === 'loading') {
    return <div className="p-8">Loading...</div>;
  }

  if (!isAuthenticated) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Token Refresh Test</h1>
        <p className="text-red-600">Please sign in to test token refresh functionality.</p>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Token Refresh Test Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Test Controls */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
          <div className="space-y-3">
            <button
              onClick={testGetToken}
              disabled={loading}
              className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              Test Get Token
            </button>
            <button
              onClick={testAuthenticatedFetch}
              disabled={loading}
              className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
            >
              Test Authenticated Fetch
            </button>
            <button
              onClick={testRefreshTokenDirectly}
              disabled={loading}
              className="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
            >
              Test Direct Refresh
            </button>
            <button
              onClick={testMiddlewareRefresh}
              disabled={loading}
              className="w-full bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50"
            >
              Test Middleware Refresh
            </button>
            <button
              onClick={testMobileRefresh}
              disabled={loading}
              className="w-full bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 disabled:opacity-50"
            >
              Test Mobile Refresh
            </button>
            <button
              onClick={clearResults}
              className="w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Clear Results
            </button>
          </div>
        </div>

        {/* Current Token Info */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Current Token Info</h2>

          {/* Token Manager Status */}
          <div className="mb-4 p-3 bg-gray-50 rounded">
            <h3 className="font-medium mb-2">Token Manager Status</h3>
            <div className="space-y-1 text-sm">
              <p><strong>Is Refreshing:</strong>
                <span className={isRefreshing ? 'text-orange-600' : 'text-green-600'}>
                  {isRefreshing ? 'Yes' : 'No'}
                </span>
              </p>
              <p><strong>Needs Refresh:</strong>
                <span className={needsRefresh ? 'text-red-600' : 'text-green-600'}>
                  {needsRefresh ? 'Yes' : 'No'}
                </span>
              </p>
            </div>
          </div>

          {tokenInfo ? (
            <div className="space-y-2 text-sm">
              <p><strong>Email:</strong> {tokenInfo.email}</p>
              <p><strong>Role:</strong> {tokenInfo.role}</p>
              <p><strong>Expires:</strong> {tokenInfo.exp}</p>
              <p><strong>Time Until Expiry:</strong>
                <span className={tokenInfo.timeUntilExpiry < 300 ? 'text-red-600' : 'text-green-600'}>
                  {tokenInfo.timeUntilExpiry} seconds
                </span>
              </p>
              {tokenInfo.error && <p className="text-red-600"><strong>Error:</strong> {tokenInfo.error}</p>}
            </div>
          ) : (
            <p className="text-gray-500">No token information available. Click "Test Get Token" to load.</p>
          )}
        </div>
      </div>

      {/* Test Results */}
      <div className="mt-8 bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Test Results</h2>
        {testResults.length === 0 ? (
          <p className="text-gray-500">No test results yet. Run some tests to see results here.</p>
        ) : (
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded border-l-4 ${
                  result.success 
                    ? 'bg-green-50 border-green-500 text-green-800' 
                    : 'bg-red-50 border-red-500 text-red-800'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <strong>{result.test}</strong>
                    <span className={`ml-2 px-2 py-1 text-xs rounded ${
                      result.success ? 'bg-green-200' : 'bg-red-200'
                    }`}>
                      {result.success ? 'SUCCESS' : 'FAILED'}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">{result.timestamp}</span>
                </div>
                <p className="mt-1 text-sm">{result.message}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-8 bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">How to Test</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Click "Test Get Token" to retrieve and display current token information</li>
          <li>Click "Test Authenticated Fetch" to test API calls with automatic token refresh via AuthContext</li>
          <li>Click "Test Direct Refresh" to manually test the web refresh token endpoint</li>
          <li>Click "Test Middleware Refresh" to test automatic token refresh in middleware (web only)</li>
          <li>Click "Test Mobile Refresh" to test the mobile-specific refresh token endpoint</li>
          <li>Watch the console for detailed logging during token operations</li>
          <li>If your token is close to expiry (less than 15 seconds), the system should automatically refresh it</li>
          <li><strong>Note:</strong> Mobile apps should use the mobile refresh endpoint and handle their own token storage</li>
        </ol>
      </div>
    </div>
  );
}
