import { createContext, useContext, useEffect, useState } from 'react';
import { useSession, signIn, signOut } from "next-auth/react";
import { useRouter } from 'next/router';
import { jwtDecode } from "jwt-decode";

const AuthContext = createContext();

// Configurable buffer time
const TOKEN_REFRESH_BUFFER = 15 * 1000; // 30 seconds in milliseconds

export function AuthProvider({ children }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [currentToken, setCurrentToken] = useState(null);

  const refreshToken = async () => {
    const refreshTokenValue = session?.refreshToken || session?.user?.refreshToken;
    console.log('Attempting token refresh for session:', !!session);

    if (!refreshTokenValue) {
      console.error('No refresh token available in session');
      throw new Error('No refresh token available');
    }

    try {
      console.log('Calling refresh token API...');
      const response = await fetch('/api/auth/refresh-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: refreshTokenValue }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Token refresh failed:', response.status, errorData);

        // Handle specific error cases
        if (response.status === 401) {
          console.log('Refresh token expired or invalid, signing out');
          signOut({ callbackUrl: "/auth/signin" });
          throw new Error('Refresh token expired');
        }

        throw new Error(`Failed to refresh token: ${errorData.message || 'Unknown error'}`);
      }

      const { token: newToken, message } = await response.json();
      console.log('Token refresh successful:', message);

      setCurrentToken(newToken);
      return newToken;
    } catch (error) {
      console.error('Token refresh error:', error);

      // Only sign out if it's an authentication error
      if (error.message.includes('expired') || error.message.includes('invalid')) {
        signOut({ callbackUrl: "/auth/signin" });
      }

      throw error;
    }
  };

  const getToken = async () => {
    console.log('getToken called - session status:', status);

    // If we have a current token, check if it's still valid
    if (currentToken) {
      try {
        const decodedToken = jwtDecode(currentToken);
        const tokenExp = new Date(decodedToken.exp * 1000);
        const timeUntilExpiry = tokenExp.getTime() - Date.now();

        if (timeUntilExpiry > TOKEN_REFRESH_BUFFER) {
          console.log('Current token still valid, time remaining:', Math.floor(timeUntilExpiry / 1000), 'seconds');
          return currentToken;
        } else {
          console.log('Current token expires soon, will refresh');
        }
      } catch (error) {
        console.error('Current token decode failed:', error);
        setCurrentToken(null);
      }
    }

    // Check if we have a session
    if (status === 'loading') {
      console.log('Session still loading, waiting...');
      return null;
    }

    if (status === 'unauthenticated' || !session) {
      console.log('No session, redirecting to signin');
      router.push('/auth/signin');
      return null;
    }

    // Get session token
    const sessionToken = session?.token || session?.user?.token;

    if (!sessionToken) {
      console.log('No session token available, redirecting to signin');
      router.push('/auth/signin');
      return null;
    }

    // Check session token validity
    try {
      const sessionTokenDecoded = jwtDecode(sessionToken);
      const sessionTokenExp = new Date(sessionTokenDecoded.exp * 1000);
      const timeUntilExpiry = sessionTokenExp.getTime() - Date.now();

      if (timeUntilExpiry > TOKEN_REFRESH_BUFFER) {
        console.log('Using valid session token');
        setCurrentToken(sessionToken);
        return sessionToken;
      } else {
        console.log('Session token expires soon, attempting refresh');
      }
    } catch (error) {
      console.error('Session token decode failed:', error);
    }

    // Try to refresh the token
    try {
      console.log('Attempting to refresh token...');
      const newToken = await refreshToken();
      return newToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      // Don't redirect here as refreshToken already handles sign out
      return null;
    }
  };

  const login = async (email, password) => {
    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });
      if (result.error) throw new Error('Invalid email or password'); //result.error
      return result;
    } catch (error) {
      throw error;
    }
  };

  const loginWithGoogle = async () => {
    try {
      await signIn("google", { callbackUrl: "/app/dashboard" });
    } catch (error) {
      throw error;
    }
  };

  const logout = () => {
    signOut({ callbackUrl: "/" });
  };

  const register = async (userData) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Registration failed');
      }

      const data = await response.json();
      
      // Auto login after successful registration
      if (!data.isInvited) {
        const loginResult = await signIn("credentials", {
          email: userData.email,
          password: userData.password,
          redirect: false,
        });

        if (loginResult.error) {
          throw new Error('Auto login failed');
        }
      }

      return { ...data, success: true };
    } catch (error) {
      throw error;
    }
  };

  const forgotPassword = async (email) => {
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to process password reset request');
      }

      return response.json();
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (token, password) => {
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to reset password');
      }

      return response.json();
    } catch (error) {
      throw error;
    }
  };

  // Utility function for making authenticated API calls with automatic token refresh
  const authenticatedFetch = async (url, options = {}) => {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No valid token available');
      }

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      };

      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Check if we got a new token in response headers
      const newToken = response.headers.get('X-New-Token');
      if (newToken) {
        console.log('Received new token from server, updating current token');
        setCurrentToken(newToken);
      }

      // Handle 401 errors by attempting token refresh
      if (response.status === 401) {
        console.log('Received 401, attempting token refresh...');
        try {
          const refreshedToken = await refreshToken();
          if (refreshedToken) {
            // Retry the request with the new token
            const retryHeaders = {
              ...headers,
              'Authorization': `Bearer ${refreshedToken}`,
            };

            const retryResponse = await fetch(url, {
              ...options,
              headers: retryHeaders,
            });

            return retryResponse;
          }
        } catch (refreshError) {
          console.error('Token refresh failed during authenticated fetch:', refreshError);
          throw new Error('Authentication failed and token refresh unsuccessful');
        }
      }

      return response;
    } catch (error) {
      console.error('Authenticated fetch error:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      user: session?.user,
      status,
      isAuthenticated: !!session,
      token: currentToken || session?.token || session?.user?.token,
      getToken,
      authenticatedFetch,
      login,
      logout,
      loginWithGoogle,
      register,
      forgotPassword,
      resetPassword
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
