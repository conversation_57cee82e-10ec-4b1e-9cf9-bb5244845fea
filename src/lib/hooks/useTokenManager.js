import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { jwtDecode } from 'jwt-decode';

const TOKEN_REFRESH_BUFFER = 15 * 1000; // 15 seconds

export function useTokenManager() {
  const { data: session, status, update } = useSession();
  const [currentToken, setCurrentToken] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Get the most current token
  const getCurrentToken = useCallback(() => {
    return currentToken || session?.token || session?.user?.token;
  }, [currentToken, session]);

  // Check if token needs refresh
  const needsRefresh = useCallback((token) => {
    if (!token) return true;
    
    try {
      const decoded = jwtDecode(token);
      const timeUntilExpiry = (decoded.exp * 1000) - Date.now();
      return timeUntilExpiry <= TOKEN_REFRESH_BUFFER;
    } catch (error) {
      console.error('Token decode error:', error);
      return true;
    }
  }, []);

  // Refresh token function
  const refreshToken = useCallback(async () => {
    if (isRefreshing) {
      // If already refreshing, wait for it to complete
      return new Promise((resolve) => {
        const checkRefresh = () => {
          if (!isRefreshing) {
            resolve(getCurrentToken());
          } else {
            setTimeout(checkRefresh, 100);
          }
        };
        checkRefresh();
      });
    }

    setIsRefreshing(true);
    
    try {
      const refreshTokenValue = session?.refreshToken || session?.user?.refreshToken;
      
      if (!refreshTokenValue) {
        throw new Error('No refresh token available');
      }

      console.log('Refreshing token...');
      const response = await fetch('/api/auth/refresh-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: refreshTokenValue }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Token refresh failed: ${errorData.message || 'Unknown error'}`);
      }

      const { token: newToken } = await response.json();
      console.log('Token refreshed successfully');
      
      // Update current token
      setCurrentToken(newToken);
      
      // Update session with new token
      await update({
        ...session,
        token: newToken,
        user: {
          ...session?.user,
          token: newToken
        }
      });

      return newToken;
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    } finally {
      setIsRefreshing(false);
    }
  }, [session, isRefreshing, getCurrentToken, update]);

  // Get valid token (refresh if needed)
  const getValidToken = useCallback(async () => {
    const token = getCurrentToken();
    
    if (!token || needsRefresh(token)) {
      return await refreshToken();
    }
    
    return token;
  }, [getCurrentToken, needsRefresh, refreshToken]);

  // Enhanced fetch with automatic token management
  const authenticatedFetch = useCallback(async (url, options = {}) => {
    try {
      const token = await getValidToken();
      
      if (!token) {
        throw new Error('No valid token available');
      }

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      };

      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Check for new token from middleware
      const newToken = response.headers.get('X-New-Token');
      if (newToken) {
        console.log('Received new token from middleware');
        setCurrentToken(newToken);
        
        // Update session with new token
        await update({
          ...session,
          token: newToken,
          user: {
            ...session?.user,
            token: newToken
          }
        });
      }

      // Handle 401 by attempting one more refresh
      if (response.status === 401 && !options._isRetry) {
        console.log('Received 401, attempting token refresh...');
        try {
          const refreshedToken = await refreshToken();
          if (refreshedToken) {
            // Retry the request with new token
            return authenticatedFetch(url, {
              ...options,
              _isRetry: true,
              headers: {
                ...options.headers,
                'Authorization': `Bearer ${refreshedToken}`,
              }
            });
          }
        } catch (refreshError) {
          console.error('Token refresh failed during 401 retry:', refreshError);
        }
      }

      return response;
    } catch (error) {
      console.error('Authenticated fetch error:', error);
      throw error;
    }
  }, [getValidToken, refreshToken, session, update]);

  // Initialize current token from session
  useEffect(() => {
    if (session?.token && !currentToken) {
      setCurrentToken(session.token);
    }
  }, [session, currentToken]);

  return {
    currentToken: getCurrentToken(),
    getValidToken,
    authenticatedFetch,
    isRefreshing,
    needsRefresh: needsRefresh(getCurrentToken())
  };
}
