export const appvars = {
    appname : 'YODA',
    port: '3000',
}
const appname = 'yoda';

export const vars = {
    db : {
        dbName: 'subanet',
        // TflouuDBName: 'toursdb',
        collection: {
            modules: appname + '.main.dim.app.modules',
            users: appname + '.main.dim.users',
            invitations: appname + '.main.dim.users.invitations',
            customers: appname + '.main.dim.customers',
            variables: appname + '.main.dim.variables',
            pricingPlans: appname + '.main.dim.pricingPlans', // tflouu.main.dim.priceplans
            _act_schemaInfo: '.a.schemainfo',
        },
        actCollections: {
            schemaInfo: '.a.schemainfo',
            workspaces: '.app.workspaces',
            workspaceselections: '.app.workspaces.selections',
            organizations: '.app.organizations',
            position: '.app.auth.positions',
            position_titles: '.app.auth.position_titles',
            // position_roles: '.app.auth.position_roles',
            roles: '.app.auth.roles',
            departments_lines: '.app.org.departments_lines',
            permissions: '.app.auth.permissions',
        },
    },
    token : {
        tokenlifeTime: '30m',
        refreshtokenLifeTime: '30d',
    }
}

export const siteConfig = {
    name: "Insights",
    url: "https://yoda.subanet.com",
    description: "The only reporting and audit dashboard you will ever need.",
  }

