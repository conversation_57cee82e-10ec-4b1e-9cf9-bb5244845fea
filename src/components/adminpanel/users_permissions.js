import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';
import { useToast } from '@/lib/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from '@/components/ui/table';

const PermissionsCard = (props) => {
    const {session} = props;
    const [loading, setLoading] = useState(true);
    const [updating, setUpdating] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [newPositionTitle, setNewPositionTitle] = useState('');
    const [selectedPosition, setSelectedPosition] = useState(null);
    const { post, get, put, delete: deleteRequest } = useApiClient();
    const { toast } = useToast();
    const [permissionsData, setPermissionsData] = useState(props.permissions || []);


    const updatePermissions = async () => {
        setUpdating(true);
        try {
            const res = await fetch('/api/tenant/permissions', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                toast({
                    title: "Success",
                    description: "Permissions updated successfully",
                });
                console.log('Permissions updated:', data);
                // Refresh the permissions data after update
                await getPermissions();
            } else {
                toast({
                    title: "Error",
                    description: "Failed to update permissions",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error('Error updating permissions:', error);
            toast({
                title: "Error",
                description: "Failed to update permissions",
                variant: "destructive",
            });
        } finally {
            setUpdating(false);
        }
    };

    const getPermissions = async () => {
        try {
            // console.log('date ', new Date(Date.now()), 'getPermissions');
            const res = await fetch('/api/tenant/permissions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                console.log('permissions:', data);
                setPermissionsData(data.data || []);
                props.setPermissions && props.setPermissions(data.data || []);
                toast({
                    title: "Success",
                    description: "Permissions fetched successfully",
                });
            }
            setLoading(false);
        } catch (error) {
            console.error('Error fetching customer info:', error);
            setLoading(false);
        }
    };

    // useEffect(() => {
    //     if (props.session?.token && !props.permissions && (Array.isArray(permissionsData) && permissionsData.length == 0)) {
    //         getPermissions();
    //     }
    // }, [props.session?.token]);


    useEffect(() => {
        if (props.permissions) {
            setPermissionsData(props.permissions || []);
            setLoading(false);
        } else {
            if (props.session?.token &&  (Array.isArray(permissionsData) && permissionsData.length == 0)) {
            getPermissions();
        }
        }
    }, [props.permissions]);

    // Group permissions by module
    const groupedPermissions = permissionsData.reduce((acc, permission) => {
        const moduleKey = permission.module || 'unknown';
        if (!acc[moduleKey]) {
            acc[moduleKey] = {
                module: permission.module,
                moduleTitle: permission.moduleTitle || permission.module,
                permissions: []
            };
        }
        acc[moduleKey].permissions.push(permission);
        return acc;
    }, {});

    // Sort modules by module title
    const sortedModules = Object.values(groupedPermissions).sort((a, b) => 
        (a.moduleTitle || '').localeCompare(b.moduleTitle || '')
    );

    // Sort permissions within each module by code
    sortedModules.forEach(module => {
        module.permissions.sort((a, b) => (a.code || '').localeCompare(b.code || ''));
    });

    if (loading) {
        return (
            <div className="p-4 bg-white rounded-lg border border-gray-200">
                <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium">Permissions</h2>
                <Button type="button" disabled>
                    Refresh
                </Button>
            </div>

            <div className="flex items-center justify-center h-64">
            
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600"></div>
            </div>
            </div>
        );
    }
    return (
        <div className="p-4 bg-white rounded-lg border border-gray-200">
            <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium cursor-pointer" onClick={() => getPermissions()}>Permissions</h2>
                <Button type="button" onClick={updatePermissions} disabled={updating}>
                    {updating ? (
                        <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Updating...
                        </>
                    ) : (
                        'Refresh'
                    )}
                </Button>
            </div>
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 relative">
                {sortedModules.map((module) => (
                    <Card key={module.module} className="max-h-[400px] flex flex-col pb-4">
                        <CardHeader className="p-0 px-6">
                            <CardTitle className="text-lg pt-4">{module.moduleTitle}</CardTitle>
                        </CardHeader>
                        <CardContent className="flex-grow overflow-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="p-1">Code</TableHead>
                                        <TableHead className="p-1">Description</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {module.permissions.map((permission) => (
                                        <TableRow key={permission._id}>
                                            <TableCell className="p-2 font-mono text-sm">{permission.code}</TableCell>
                                            <TableCell className="p-2 text-sm">{permission.desc}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                ))}
                {sortedModules.length === 0 && (
                    <div className="col-span-full text-center py-8 text-gray-500">
                        No permissions found
                    </div>
                )}
                {updating && (
                    <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10 rounded-lg">
                        <div className="flex flex-col items-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mb-2"></div>
                            <p className="text-gray-700">Updating permissions...</p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}

export default PermissionsCard;
