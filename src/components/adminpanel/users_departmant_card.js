import { useState, useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {    // <-- Use this
    PencilIcon
} from '@heroicons/react/24/outline';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"

const DepartmentzCard = ({ departments, session, getCustomerUsersInfo }) => {
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [selectedDepartment, setSelectedDepartment] = useState(null);
    const [departmentz, setDepartmentz] = useState(departments || []);
    const [newDepartment, setNewDepartment] = useState({
        name: '',
        code: '',
        type: 'departmant', // default to 'departman'
        parentId: ''
    });
    const [editDepartment, setEditDepartment] = useState({
        name: '',
        code: '',
        type: 'departmant',
        parentId: ''
    });
    const [parentSearchTerm, setParentSearchTerm] = useState('');
    const [editParentSearchTerm, setEditParentSearchTerm] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const departmentsPerPage = 15;

    // Filter departments based on search term for parent selection
    const filteredDepartments = (Array.isArray(departmentz) ? departmentz : [])
        .filter(dept =>
            dept.name?.toLowerCase().includes(parentSearchTerm.toLowerCase())
        ) || [];

    // Filter departments for edit modal parent selection
    const filteredEditDepartments = (Array.isArray(departmentz) ? departmentz : [])
        .filter(dept =>
            dept.name?.toLowerCase().includes(editParentSearchTerm.toLowerCase())
        ) || [];

    // Initialize edit department data when modal opens
    useEffect(() => {
        if (isEditModalOpen && selectedDepartment) {
            // Ensure type is one of the valid options
            const validTypes = ['directorate', 'departmant', 'line'];
            const departmentType = selectedDepartment.type && validTypes.includes(selectedDepartment.type) 
                ? selectedDepartment.type 
                : 'departmant';
                
            setEditDepartment({
                id: selectedDepartment._id,
                name: selectedDepartment.name || '',
                code: selectedDepartment.code || '',
                type: departmentType,
                parentId: selectedDepartment.parentId || ''
            });
        }
    }, [isEditModalOpen, selectedDepartment]);

    const handleAddDepartmentChange = (e) => {
        const { name, value } = e.target;
        setNewDepartment(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleParentSelect = (deptId) => {
        // console.log('handleParentSelect deptId', deptId);
        setNewDepartment(prev => ({
            ...prev,
            parentId: deptId
        }));
    };

    const getDepartmentz = async () => {
        try {
            const res = await fetch('/api/tenant/departmentz', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                // console.log('Departmentz:', data);
                
                // Sort by type first, then by name
                const sortedData = (data.data || []).sort((a, b) => {

                    if (a.code < b.code) return -1;
                    if (a.code > b.code) return 1;

                    if (a.type < b.type) return -1;
                    if (a.type > b.type) return 1;
                    if (a.name < b.name) return -1;
                    if (a.name > b.name) return 1;
                    return 0;
                });
                
                setDepartmentz(sortedData);
            }
        } catch (error) {
            console.error('Error fetching customer info:', error);
        }
    };
    const handleAddDepartment = async () => {
        try {
            const response = await fetch('/api/tenant/departmentz', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                },
                body: JSON.stringify({
                    ...newDepartment,
                    createdBy: session?.user?.id
                })
            });

            if (response.ok) {
                // Close the modal
                setIsAddModalOpen(false);
                // Reset form
                setNewDepartment({
                    name: '',
                    code: '',
                    type: 'departmant',
                    parentId: ''
                });
                // Refresh department info
                getDepartmentz();
            } else {
                console.error('Failed to add department');
            }
        } catch (error) {
            console.error('Error adding department:', error);
        }
    };

    const handleCloseModal = () => {
        setIsAddModalOpen(false);
        setNewDepartment({
            name: '',
            code: '',
            type: 'departmant',
            parentId: ''
        });
    };

    return (
        <div className="border rounded-md p-4">
            <div className="bg-white rounded-lg ">
                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium">Departments</h2>
                    <Button onClick={() => setIsAddModalOpen(true)}>
                        Add New Department
                    </Button>
                </div>
                {/* <p className="text-gray-500">Department management content will go here.</p> */}

                {departmentz && Array.isArray(departmentz) && departmentz.length > 0 ? (
                    <div className="space-y-4">
                        {/* Search Input */}
                        <div className="flex justify-between items-center">
                            <div className="relative w-full max-w-sm">
                                <Input
                                    type="text"
                                    placeholder="Search departments..."
                                    value={searchTerm}
                                    onChange={(e) => {
                                        setSearchTerm(e.target.value);
                                        setCurrentPage(1);
                                    }}
                                    className="pl-10"
                                />
                                <svg
                                    className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                    />
                                </svg>
                            </div>
                        </div>

                        {/* Departments Table */}
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[200px]">Department Name</TableHead>
                                    <TableHead>Code</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Parent Department</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {(() => {
                                    // Filter departments based on search term
                                    const filteredDepartments = departmentz.filter(dept =>
                                        dept.name?.toLowerCase().includes(searchTerm.toLowerCase())
                                    );
                                    
                                    // Calculate pagination
                                    const totalPages = Math.ceil(filteredDepartments.length / departmentsPerPage);
                                    const indexOfLastDepartment = currentPage * departmentsPerPage;
                                    const indexOfFirstDepartment = indexOfLastDepartment - departmentsPerPage;
                                    const currentDepartments = filteredDepartments.slice(indexOfFirstDepartment, indexOfLastDepartment);
                                    
                                    return currentDepartments.map((dept) => (
                                        <TableRow key={dept._id}>
                                            <TableCell className="font-medium">{dept.name}</TableCell>
                                            <TableCell className="capitalize">{dept.code}</TableCell>
                                            <TableCell className="capitalize">{dept.type}</TableCell>
                                            <TableCell>
                                                {dept.parentId ? (
                                                    departmentz.find(d => d._id === dept.parentId)?.name || 'N/A'
                                                ) : (
                                                    '-'
                                                )}
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <Button 
                                                    variant="outline" 
                                                    size="sm"
                                                    onClick={() => {
                                                        setSelectedDepartment(dept);
                                                        setIsEditModalOpen(true);
                                                    }}
                                                >
                                                    <PencilIcon className="h-4 w-4" />
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ));
                                })()}
                            </TableBody>
                        </Table>

                        {/* Pagination */}
                        {(() => {
                            // Filter departments based on search term
                            const filteredDepartments = departmentz.filter(dept =>
                                dept.name?.toLowerCase().includes(searchTerm.toLowerCase())
                            );
                            
                            // Calculate pagination
                            const totalPages = Math.ceil(filteredDepartments.length / departmentsPerPage);
                            
                            return totalPages > 1 && (
                                <div className="flex items-center justify-between border-t border-gray-200 pt-4">
                                    <div className="text-sm text-gray-700">
                                        Showing{' '}
                                        <span className="font-medium">
                                            {Math.min((currentPage - 1) * departmentsPerPage + 1, filteredDepartments.length)}
                                        </span>{' '}
                                        to{' '}
                                        <span className="font-medium">
                                            {Math.min(currentPage * departmentsPerPage, filteredDepartments.length)}
                                        </span>{' '}
                                        of <span className="font-medium">{filteredDepartments.length}</span> departments
                                    </div>
                                    <div className="flex space-x-2">
                                        <Button
                                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                            disabled={currentPage === 1}
                                            variant="outline"
                                            size="sm"
                                        >
                                            Previous
                                        </Button>
                                        {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                                            // Show first pages, current page, and last pages
                                            let pageNum;
                                            if (totalPages <= 5) {
                                                pageNum = i + 1;
                                            } else if (currentPage <= 3) {
                                                pageNum = i + 1;
                                            } else if (currentPage >= totalPages - 2) {
                                                pageNum = totalPages - 4 + i;
                                            } else {
                                                pageNum = currentPage - 2 + i;
                                            }
                                            
                                            return (
                                                <Button
                                                    key={pageNum}
                                                    onClick={() => setCurrentPage(pageNum)}
                                                    variant={currentPage === pageNum ? "default" : "outline"}
                                                    size="sm"
                                                >
                                                    {pageNum}
                                                </Button>
                                            );
                                        })}
                                        <Button
                                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                            disabled={currentPage === totalPages}
                                            variant="outline"
                                            size="sm"
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            );
                        })()}
                    </div>
                ) : (
                    <p className="text-gray-500">No department information available.</p>
                )}

            </div>

            {/* Add Department Modal */}
            <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>Add New Department</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div>
                            <Label className="text-sm font-medium">Department Name</Label>
                            <Input
                                name="name"
                                value={newDepartment.name}
                                onChange={handleAddDepartmentChange}
                                placeholder="Enter department name"
                                className="mt-1"
                            />
                        </div>

                        <div>
                            <Label className="text-sm font-medium">Box Type</Label>
                            <select
                                name="type"
                                value={newDepartment.type}
                                onChange={handleAddDepartmentChange}
                                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-1 focus:ring-ring"
                            >
                                <option value="directorate">Direktorluk</option>
                                <option value="departmant">Departman</option>
                                <option value="line">Line</option>
                            </select>
                        </div>

                        <div>
                            <Label className="text-sm font-medium">Department Code</Label>
                            <Input
                                name="code"
                                value={newDepartment.code}
                                onChange={handleAddDepartmentChange}
                                placeholder="Enter department code - S001000"
                                className="mt-1"
                            />
                        </div>

                        <div>
                            <Label className="text-sm font-medium">Parent Department (Optional)</Label>
                            <div className="relative mt-1">
                                <Input
                                    type="text"
                                    placeholder="Search parent department..."
                                    value={parentSearchTerm}
                                    onChange={(e) => setParentSearchTerm(e.target.value)}
                                    className="mb-2"
                                />
                                <div className="max-h-40 overflow-y-auto border rounded-md">
                                    {filteredDepartments.map((dept) => (
                                        <div 
                                            key={dept._id} 
                                            className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
                                            onClick={() => handleParentSelect(dept._id)}
                                        >
                                            <input
                                                type="radio"
                                                name="parentId"
                                                checked={newDepartment.parentId === dept._id}
                                                onChange={(e) => {
                                                    e.stopPropagation();
                                                    handleParentSelect(dept._id);
                                                }}
                                                className="mr-2"
                                            />
                                            <span className="text-sm">{dept.name}</span>
                                        </div>
                                    ))}
                                    {filteredDepartments.length === 0 && parentSearchTerm && (
                                        <div className="p-2 text-sm text-gray-500">
                                            No departments found
                                        </div>
                                    )}
                                </div>
                                {newDepartment.parentId && (
                                    <div className="mt-2">
                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Selected: {filteredDepartments.find(d => d._id === newDepartment.parentId)?.name || 'Unknown'}
                                            <button
                                                type="button"
                                                className="ml-1 inline-flex items-center justify-center rounded-full bg-blue-200 text-blue-800 hover:bg-blue-300 focus:outline-none"
                                                onClick={() => handleParentSelect('')}
                                            >
                                                <span className="sr-only">Remove</span>
                                                <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </button>
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={handleCloseModal}>
                            Cancel
                        </Button>
                        <Button 
                            onClick={handleAddDepartment}
                            disabled={!newDepartment.name}
                        >
                            Add Department
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Edit Department Modal */}
            <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>Edit Department</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div>
                            <Label className="text-sm font-medium">Department Name</Label>
                            <Input
                                name="name"
                                value={editDepartment.name}
                                onChange={(e) => setEditDepartment({...editDepartment, name: e.target.value})}
                                placeholder="Enter department name"
                                className="mt-1"
                            />
                        </div>

                        <div>
                            <Label className="text-sm font-medium">Department Type</Label>
                            <select
                                name="type"
                                value={editDepartment.type}
                                onChange={(e) => setEditDepartment({...editDepartment, type: e.target.value})}
                                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-1 focus:ring-ring"
                            >
                                <option value="directorate">Direktorluk</option>
                                <option value="departmant">Departman</option>
                                <option value="line">Line</option>
                            </select>
                        </div>

                        <div>
                            <Label className="text-sm font-medium">Department Code</Label>
                            <Input
                                name="code"
                                value={editDepartment.code}
                                onChange={(e) => setEditDepartment({...editDepartment, code: e.target.value})}
                                placeholder="Enter department code - S001000"
                                className="mt-1"
                            />
                        </div>
                        <div>
                            <Label className="text-sm font-medium">Parent Department (Optional)</Label>
                            <div className="relative mt-1">
                                <Input
                                    type="text"
                                    placeholder="Search parent department..."
                                    value={editParentSearchTerm}
                                    onChange={(e) => setEditParentSearchTerm(e.target.value)}
                                    className="mb-2"
                                />
                                <div className="max-h-40 overflow-y-auto border rounded-md">
                                    { [...filteredEditDepartments].filter(dept => dept._id !== editDepartment.id).map((dept) => (
                                        <div 
                                            key={dept._id} 
                                            className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
                                            onClick={() => setEditDepartment({...editDepartment, parentId: dept._id})}
                                        >
                                            <input
                                                type="radio"
                                                name="parentId"
                                                checked={editDepartment.parentId === dept._id}
                                                onChange={(e) => {
                                                    e.stopPropagation();
                                                    setEditDepartment({...editDepartment, parentId: dept._id});
                                                }}
                                                className="mr-2"
                                            />
                                            <span className="text-sm">{dept.name} </span>
                                        </div>
                                    ))}
                                    {filteredEditDepartments.length === 0 && editParentSearchTerm && (
                                        <div className="p-2 text-sm text-gray-500">
                                            No departments found
                                        </div>
                                    )}
                                </div>
                                {editDepartment.parentId && (
                                    <div className="mt-2">
                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Selected: {filteredEditDepartments.find(d => d._id === editDepartment.parentId)?.name || 'Unknown'}
                                            <button
                                                type="button"
                                                className="ml-1 inline-flex items-center justify-center rounded-full bg-blue-200 text-blue-800 hover:bg-blue-300 focus:outline-none"
                                                onClick={() => setEditDepartment({...editDepartment, parentId: ''})}
                                            >
                                                <span className="sr-only">Remove</span>
                                                <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </button>
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <div className="flex justify-between w-full">
                            <div>

                                <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
                                    Delete
                                </Button>

                            </div>
                            <div className="flex space-x-2">
                                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                                    Cancel
                                </Button>
                                <Button
                                    onClick={async () => {
                                        try {
                                            const response = await fetch('/api/tenant/departmentz', {
                                                method: 'PATCH',
                                                headers: {
                                                    'Content-Type': 'application/json',
                                                    'Authorization': `Bearer ${session?.token}`
                                                },
                                                body: JSON.stringify({
                                                    departmentId: selectedDepartment._id,
                                                    ...editDepartment
                                                })
                                            });

                                            if (response.ok) {
                                                // Close the modal
                                                setIsEditModalOpen(false);
                                                // Refresh department info
                                                getDepartmentz();
                                            } else {
                                                console.error('Failed to update department');
                                            }
                                        } catch (error) {
                                            console.error('Error updating department:', error);
                                        }
                                    }}
                                >
                                    Save Changes
                                </Button>
                            </div>
                        </div>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure you want to delete this department?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the department 
                            "{selectedDepartment?.name}" and set its status to deleted.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                            onClick={async () => {
                                try {
                                    const response = await fetch('/api/tenant/departmentz', {
                                        method: 'DELETE',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'Authorization': `Bearer ${session?.token}`
                                        },
                                        body: JSON.stringify({
                                            departmentId: selectedDepartment._id
                                        })
                                    });

                                    if (response.ok) {
                                        // Close the dialogs
                                        setIsDeleteDialogOpen(false);
                                        setIsEditModalOpen(false);
                                        // Refresh department info
                                        getDepartmentz();
                                    } else {
                                        console.error('Failed to delete department');
                                    }
                                } catch (error) {
                                    console.error('Error deleting department:', error);
                                }
                            }}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
};

export default DepartmentzCard;