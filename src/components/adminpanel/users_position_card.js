import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { SearchableSelect } from '@/components/ui/searchable-select';
import {
    PencilIcon
} from '@heroicons/react/24/outline';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { useToast } from "@/lib/hooks/use-toast"
import { DialogDescription } from '@radix-ui/react-dialog';
import { Textarea } from '@headlessui/react';

const PositionzCard = (props) => {
    const { positionz, session, getCustomerUsersInfo } = props;
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [selectedPosition, setSelectedPosition] = useState(null);
    const [positionTitles, setPositionTitles] = useState(props.positionTitles || []);
    const [positions, setPositions] = useState(props.positions || []);
    const [roles, setRoles] = useState(props.roles || []);

    const [parentSearchTerm, setParentSearchTerm] = useState('');
    const [editParentSearchTerm, setEditParentSearchTerm] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const positionsPerPage = 15;

    const [departments, setDepartments] = useState(props.departmentz || []);

    const [newPosition, setNewPosition] = useState({
        position_title_id: '',
        description: '',
        level: '',
        grade: '',
        parent_position_id: '',
        department_id: '',
        role_ids: [],
    });
    const { post, get, put } = useApiClient();
    const { toast } = useToast();

    useEffect(() => {
        if (props.positions) {
            setPositions(props.positions);
        }
        if (props.positionTitles) {
            setPositionTitles(props.positionTitles);
        }
    }, [props.positions, props.positionTitles]);

    const fetchData = async () => {
            try {
                // Fetch position titles
                const titlesResponse = await post('/api/tenant/position_titles');
                if (titlesResponse && titlesResponse.data) {
                    setPositionTitles(titlesResponse.data);
                    props.setPositionTitles && props.setPositionTitles(titlesResponse.data);
                }
                
                // Fetch positions
                const positionsResponse = await post('/api/tenant/positions');
                if (positionsResponse && positionsResponse.data) {
                    // console.log('ositionsResponse.data', positionsResponse.data);
                    setPositions(positionsResponse.data);
                    props.setPositions && props.setPositions(positionsResponse.data);
                }

                // Fetch roles
                const rolesResponse = await post('/api/tenant/roles');
                if (rolesResponse && rolesResponse.data) {
                    setRoles(rolesResponse.data);
                    props.setRoles && props.setRoles(rolesResponse.data);
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                toast({
                    title: "Error",
                    description: "Failed to fetch data",
                    variant: "destructive",
                });
            }
        };

    // Fetch position titles and positions when component mounts
    useEffect(() => {
        if (!positions || !positionTitles || !roles) return;
        if (props.session?.token && ((Array.isArray(positions) && positions.length == 0) || (Array.isArray(positionTitles) && positionTitles.length == 0) || (Array.isArray(roles) && roles.length == 0))) {
            console.log('fetching data');
            fetchData();
        }
        // fetchData();
    }, []);

    const handleAddPosition = async () => {
        try {
            // Remove empty parent_position_id and department_id values
            const createData = { ...newPosition };
            if (createData.parent_position_id === '') {
                delete createData.parent_position_id;
            }
            if (createData.department_id === '') {
                delete createData.department_id;
            }
            
            const response = await put('/api/tenant/positions', createData);
            if (response && response.data) {
                toast({
                    title: "Success",
                    description: "Position created successfully",
                });
                setIsAddModalOpen(false);
                setNewPosition({
                    position_title_id: '',
                    description: '',
                    level: '',
                    grade: '',
                    parent_position_id: '',
                    department_id: '',
                    role_ids: []
                });
                // Refresh positions data
                getCustomerUsersInfo();
                
                // Refresh positions list
                fetchData();
                // const positionsResponse = await get('/api/tenant/positions');
                // if (positionsResponse && positionsResponse.data) {
                //     setPositions(positionsResponse.data);
                // }
            } else {
                // Handle error response
                toast({
                    title: "Error",
                    description: response?.message || "Failed to create position",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error('Error creating position:', error);
            toast({
                title: "Error",
                description: "Failed to create position",
                variant: "destructive",
            });
        }
    };

    const handleInputChange = (field, value) => {
        setNewPosition(prev => ({
            ...prev,
            [field]: value
        }));
    };


    const deletePosition = async () => {
        try {
            if (confirm('Are you sure you want to delete this position?')) {
                const res = await fetch(`/api/tenant/positions/${selectedPosition._id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${session?.token}`
                    },
                    body: JSON.stringify({
                        action: 'delete',
                        position_id: selectedPosition._id
                    })
                });
                if (res.ok) {
                    const data = await res.json();
                    toast({
                        title: "Success",
                        description: "position deleted successfully",
                    });
                    setIsEditModalOpen(false);
                    fetchData(); // Refresh positions list
                } else {
                    const errorData = await res.json();
                    toast({
                        title: "Error",
                        description: errorData.message || "Failed to delete position",
                        variant: "destructive",
                    });
                }
            } else {
                toast({
                    title: "Cancelled",
                    description: "Role deletion cancelled",
                });
                return;
            }
        } catch (error) {
            console.error('Error deleting position:', error);
            toast({
                title: "Error",
                description: "Failed to delete position",
                variant: "destructive",
            });
        }
    };

    // Function to find position title by ID
    const getPositionTitle = (id) => {
        // console.log('position Titles', positionTitles, id)
        const title = positionTitles.find(title => title._id === id);
        return title ? title.position_title : 'N/A';
    };


    // Function to find position by ID
    const getDepartment = (id) => {
        const department = departments.find(pos => pos._id === id);
        return department ? department : null;
    };

    // Function to find position by ID
    const getPosition = (id) => {
        const position = positions.find(pos => pos._id === id);
        return position ? position : null;
    };

    return (
        <div className="border rounded-md p-4">
            <div className="bg-white rounded-lg ">
                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium cursor-pointer" onClick={() => fetchData()}>Positions</h2>
                    <Button onClick={() => setIsAddModalOpen(true)}>
                        Add New Position
                    </Button>
                </div>
                
                {/* Add Position Dialog */}
                <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                    <DialogContent className="max-w-2xl">
                        <DialogDescription className="sr-only">
                            Create a new position by selecting the position title...
                        </DialogDescription>
                        <DialogHeader>
                            <DialogTitle>Add New Position</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="department" className="text-right">
                                    Cell / Box
                                </Label>
                                <div className="col-span-3">
                                    <SearchableSelect
                                        options={[{ value: '', label: 'None' }].concat(departments.filter(department => department && department._id).map(department => ({
                                            value: department._id,
                                            label: department.name ? (department.parentId ? `${department.name} (${(departments.find(d => d._id === department.parentId)?.name) || 'Unknown Parent'})` : department.name) : 'Unnamed Department'
                                        })))}
                                        value={newPosition.department_id || ''}
                                        onValueChange={(value) => handleInputChange('department_id', value)}
                                        placeholder="Select a department"
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="parent_position" className="text-right">
                                    Parent Position
                                </Label>
                                <div className="col-span-3">
                                    <SearchableSelect
                                        options={positions.filter(position => position && position._id).map(position => ({ 
                                            value: position._id, 
                                            label: `${getPositionTitle(position.position_title_id)} - Level ${position.level}` 
                                        }))}
                                        value={newPosition.parent_position_id}
                                        onValueChange={(value) => handleInputChange('parent_position_id', value)}
                                        placeholder="Select a parent position"
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="position_title" className="text-right">
                                    Position Title
                                </Label>
                                <div className="col-span-3">
                                    <SearchableSelect
                                        options={positionTitles.filter(title => title && title._id).map(title => ({ 
                                            value: title._id, 
                                            label: title.position_title || 'Unnamed Title'
                                        }))}
                                        value={newPosition.position_title_id}
                                        onValueChange={(value) => handleInputChange('position_title_id', value)}
                                        placeholder="Select a position title"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-4 items-start gap-4">
                                <Label htmlFor="description" className="text-right">
                                    Description
                                </Label>
                                <Textarea
                                    id="description"
                                    type="text"
                                    className="col-span-3 border border-1 radius-xl p-2"
                                    value={newPosition.description}
                                    onChange={(e) => handleInputChange('description', e.target.value)}
                                />
                            </div>

                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="level" className="text-right">
                                    Level
                                </Label>
                                <Input
                                    id="level"
                                    type="number"
                                    className="col-span-1"
                                    value={newPosition.level}
                                    onChange={(e) => handleInputChange('level', e.target.value)}
                                />
                                <Label htmlFor="grade" className="text-right">
                                    Grade
                                </Label>
                                <Input
                                    id="grade"
                                    type="number"
                                    className="col-span-1"
                                    value={newPosition.grade}
                                    onChange={(e) => handleInputChange('grade', e.target.value)}
                                />
                            </div>
                            {/* <div className="grid grid-cols-4 items-center gap-4">
                            </div> */}
                            <div className="grid grid-cols-4 items-start gap-4">
                                    <Label htmlFor="edit_roles" className="text-right pt-3">
                                        Roles
                                    </Label>
                                    <div className="col-span-3">
                                        <div className="border rounded-md p-3 max-h-[300px] overflow-y-auto">
                                            <div className="grid grid-cols-1 gap-2">
                                                {roles.filter(role => role && role._id).map((role) => (
                                                    <div key={role._id} className="flex items-center space-x-2">
                                                        <input
                                                            type="checkbox"
                                                            id={`role-${role._id}`}
                                                            onChange={(e) => {
                                                                const isChecked = e.target.checked;
                                                                setNewPosition(prev => {
                                                                    if ( isChecked && !prev.role_ids.includes(role._id) ) {
                                                                        return {
                                                                            ...prev,
                                                                            role_ids: [...prev.role_ids, role._id]
                                                                        };
                                                                    } else if (!isChecked && prev.role_ids.includes(role._id)) {
                                                                        return {
                                                                            ...prev,
                                                                            role_ids: prev.role_ids.filter(id => id !== role._id)
                                                                        };
                                                                    } else {
                                                                        return prev;
                                                                    }
                                                                }
                                                                );
                                                            }}
                                                            className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                                                        />
                                                        <Label 
                                                            htmlFor={`role-${role._id}`} 
                                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                                        >
                                                            {role.roleName || 'n/a'}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                                Cancel
                            </Button>
                            <Button 
                                onClick={handleAddPosition}
                                disabled={!newPosition.position_title_id || !newPosition.level || !newPosition.grade}
                            >
                                Create Position
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
                
                {/* Positions Table */}

                        {/* Search Input */}
                        <div className="flex justify-between items-center">
                            <div className="relative w-full max-w-sm">
                                <Input
                                    type="text"
                                    placeholder="Search positions..."
                                    value={searchTerm}
                                    onChange={(e) => {
                                        setSearchTerm(e.target.value);
                                        setCurrentPage(1);
                                    }}
                                    className="pl-10"
                                />
                                <svg
                                    className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                    />
                                </svg>
                            </div>
                        </div>

                <div className="mt-6">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Position Title</TableHead>
                                <TableHead>Level</TableHead>
                                <TableHead>Grade</TableHead>
                                <TableHead>Parent Position</TableHead>
                                <TableHead>Roles</TableHead>
                                <TableHead>Cell/Box</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {
                            // positions.map((position) => (
                                (() => {
                                    // Filter departments based on search term
                                    const filteredPositions = positions.filter(dept =>
                                        dept.position_title?.toLowerCase().includes(searchTerm.toLowerCase())
                                    );
                                    
                                    // Calculate pagination
                                    const totalPages = Math.ceil(filteredPositions.length / positionsPerPage);
                                    const indexOfLastDepartment = currentPage * positionsPerPage;
                                    const indexOfFirstDepartment = indexOfLastDepartment - positionsPerPage;
                                    const currentPositions = filteredPositions.slice(indexOfFirstDepartment, indexOfLastDepartment);
                                    
                                    return currentPositions.map((position) => (
                                <TableRow key={position._id}>
                                    <TableCell className="font-medium" title={position._id}>
                                        {(position.position_title || (position.position_title_txt ? 'XX ' + position.position_title_txt : 'N/A'))}
                                        <span title={position.description}>&nbsp;&nbsp;&nbsp;.</span>
                                    </TableCell>
                                    <TableCell>{position.level}</TableCell>
                                    <TableCell>{position.grade}</TableCell>
                                    <TableCell title={position.parent_position_id}>
                                        {position.parent_position_title ? 
                                            ((position.parent_position_title)) || 'N/A' : 
                                            'None'}
                                    </TableCell>
                                    <TableCell title={Array.isArray(position.role_ids) ? position.role_ids.join(',') : ''}>{Array.isArray(position.role_ids) && position.role_ids.length > 0 ? position.role_ids.length : 'None'   }</TableCell> 
                                    <TableCell title={position.department_id}>
                                        {position.department_id ?
                                            (getDepartment(position.department_id)?.name) || 'N/A' :
                                            'None'}
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <Button 
                                            variant="outline" 
                                            size="sm"
                                            onClick={() => {
                                                setSelectedPosition(position);
                                                setIsEditModalOpen(true);
                                            }}
                                        >
                                            <PencilIcon className="h-4 w-4" />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            // ))}

                                    ));
                                })()}
                        </TableBody>
                    </Table>
                </div>
                {/* <p>
                    {JSON.stringify(departments, null, 2)}
                </p> */}
                
                {/* Edit Position Dialog */}
                <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
                    <DialogContent className="max-w-2xl">
                        <DialogDescription className="sr-only">
                            Edit position by modifying the position title...
                        </DialogDescription>
                        <DialogHeader>
                            <DialogTitle>Edit Position</DialogTitle>
                        </DialogHeader>
                        {selectedPosition && (
                            <div className="grid gap-4 py-4">
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit_department" className="text-right">
                                        Cell / Box
                                    </Label>
                                    <div className="col-span-3">
                                        <SearchableSelect
                                            options={[{ value: '', label: 'None' }].concat(departments.filter(department => department && department._id).map(department => ({
                                                value: department._id,
                                                label: department.name ? (department.parentId ? `${department.name} (${(departments.find(d => d._id === department.parentId)?.name) || 'Unknown Parent'})` : department.name) : 'Unnamed Department'
                                            })))}
                                            value={selectedPosition.department_id || ''}
                                            onValueChange={(value) => setSelectedPosition({...selectedPosition, department_id: value})}
                                            placeholder="Select a department"
                                        />
                                    </div>
                                </div>
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit_parent_position" className="text-right">
                                        Parent Position
                                    </Label>
                                    <div className="col-span-3">
                                        <SearchableSelect
                                            options={[{ value: '', label: 'None' }].concat(positions.filter(position => position && position._id).map(position => ({ 
                                                value: position._id, 
                                                label: `${getPositionTitle(position.position_title_id)} - Level ${position.level}` 
                                            })))}
                                            value={selectedPosition.parent_position_id || ''}
                                            onValueChange={(value) => setSelectedPosition({...selectedPosition, parent_position_id: value})}
                                            placeholder="Select a parent position"
                                        />
                                    </div>
                                </div>
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit_position_title" className="text-right">
                                        Position Title
                                    </Label>
                                    <div className="col-span-3">
                                        <SearchableSelect
                                            options={positionTitles.filter(title => title && title._id).map(title => ({ 
                                                value: title._id, 
                                                label: title.position_title || 'Unnamed Title'
                                            }))}
                                            value={selectedPosition.position_title_id}
                                            onValueChange={(value) => setSelectedPosition({...selectedPosition, position_title_id: value})}
                                            placeholder="Select a position title"
                                        />
                                    </div>
                                </div>

                            <div className="grid grid-cols-4 items-start gap-4">
                                <Label htmlFor="description" className="text-right">
                                    Description
                                </Label>
                                <Textarea
                                    id="description"
                                    type="text"
                                    className="col-span-3 border border-1 radius-xl p-2 font-sm"
                                    value={selectedPosition.description}
                                    onChange={(e) => setSelectedPosition({...selectedPosition, description: e.target.value}) }
                                />
                            </div>

                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit_level" className="text-right">
                                        Level
                                    </Label>
                                    <Input
                                        id="edit_level"
                                        type="number"
                                        className="col-span-1"
                                        value={selectedPosition.level}
                                        onChange={(e) => setSelectedPosition({...selectedPosition, level: e.target.value})}
                                    />
                                    <Label htmlFor="edit_grade" className="text-right">
                                        Grade
                                    </Label>
                                    <Input
                                        id="edit_grade"
                                        type="number"
                                        className="col-span-1"
                                        value={selectedPosition.grade}
                                        onChange={(e) => setSelectedPosition({...selectedPosition, grade: e.target.value})}
                                    />

                                </div>
                                <div className="grid grid-cols-4 items-start gap-4">
                                    <Label htmlFor="edit_roles" className="text-right pt-3">
                                        Roles
                                    </Label>
                                    <div className="col-span-3">
                                        <div className="border rounded-md p-3 max-h-[300px] overflow-y-auto">
                                            <div className="grid grid-cols-1 gap-2">
                                                {roles.filter(role => role && role._id).map((role) => (
                                                    <div key={role._id} className="flex items-center space-x-2">
                                                        <input
                                                            type="checkbox"
                                                            id={`role-${role._id}`}
                                                            checked={Array.isArray(selectedPosition.role_ids) && selectedPosition.role_ids.includes(role._id)}
                                                            onChange={(e) => {
                                                                const isChecked = e.target.checked;
                                                                setSelectedPosition(prev => {
                                                                    const currentRoles = Array.isArray(prev.role_ids) ? [...prev.role_ids] : [];
                                                                    if (isChecked) {
                                                                        return {
                                                                            ...prev,
                                                                            role_ids: [...currentRoles, role._id]
                                                                        };
                                                                    } else {
                                                                        return {
                                                                            ...prev,
                                                                            role_ids: currentRoles.filter(id => id !== role._id)
                                                                        };
                                                                    }
                                                                });
                                                            }}
                                                            className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                                                        />
                                                        <Label 
                                                            htmlFor={`role-${role._id}`} 
                                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                                        >
                                                            {role.roleName || 'n/a'}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                        <DialogFooter>


                        <div className="flex w-full gap-2 justify-between">
                            <Button className="w-32 bg-red-300 hover:bg-red-600" variant="outline" onClick={() => deletePosition()}>
                                Delete
                            </Button>
                            <div className="flex gap-2">
                                
                            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                                Cancel
                            </Button>
                            <Button onClick={async () => {
                                try {
                                    // Remove empty parent_position_id
                                    const updateData = { ...selectedPosition };
                                    if (updateData.parent_position_id === '') {
                                        delete updateData.parent_position_id;
                                    }
                                    // Remove empty department_id
                                    if (updateData.department_id === '') {
                                        delete updateData.department_id;
                                    }
                                    
                                    const response = await put(`/api/tenant/positions/${selectedPosition._id}`, updateData);
                                    if (response && response.data) {
                                        toast({
                                            title: "Success",
                                            description: "Position updated successfully",
                                        });
                                        setIsEditModalOpen(false);
                                        setSelectedPosition(null);
                                        // Refresh positions data
                                        getCustomerUsersInfo();
                                        
                                        // Refresh positions list
                                        fetchData();
                                        // const positionsResponse = await post('/api/tenant/positions');
                                        // if (positionsResponse && positionsResponse.data) {
                                        //     setPositions(positionsResponse.data);
                                        // }
                                    }
                                } catch (error) {
                                    console.error('Error updating position:', error);
                                    toast({
                                        title: "Error",
                                        description: "Failed to update position",
                                        variant: "destructive",
                                    });
                                }
                            }}>
                                Update Position
                            </Button>
                            </div>
                        </div>


                        
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </div>
    );
};

export default PositionzCard;
